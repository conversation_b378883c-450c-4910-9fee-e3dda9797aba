'use client';

import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  CircleDollarSign,
  CircleUser,
  CircleUserRound,
} from 'lucide-react';
import PaginatedDataTable from '@/components/PaginatedDatedTable';

// Dynamically import WorldMap to avoid SSR issues with Leaflet
const WorldMap = dynamic(() => import('@/components/WorldMap'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
      Loading map...
    </div>
  ),
});

const locationData = [
  {
    id: 1,
    lat: 10.0,
    lng: -10.0,
    status: 'genuine',
    location: 'Central America',
    date: '2025-05-01',
  },
  {
    id: 2,
    lat: 20.0,
    lng: -10.0,
    status: 'tampered',
    location: 'Caribbean',
    date: '2025-05-02',
  },
  {
    id: 3,
    lat: 30.0,
    lng: -40.0,
    status: 'genuine',
    location: 'West Africa',
    date: '2025-05-03',
  },
  {
    id: 4,
    lat: 50.0,
    lng: -20.0,
    status: 'tampered',
    location: 'Western Europe',
    date: '2025-05-04',
  },
];

// Stats cards data
const statsCards = [
  {
    title: 'Total Scans',
    value: '14,568',
    change: '+20.1%',
    period: 'from last month',
    icon: <CircleUserRound className="h-5 w-5 text-muted-foreground" />,
  },
  {
    title: 'Genuine QR',
    value: '14,563',
    change: '+18.1%',
    period: 'from last month',
    icon: <CircleDollarSign className="h-5 w-5 text-muted-foreground" />,
  },
  {
    title: 'Tampered QR',
    value: '5',
    change: '****%',
    period: 'from last month',
    icon: <CircleUser className="h-5 w-5 text-muted-foreground" />,
  },
];

export default function Dashboard() {
  const [viewType, setViewType] = useState('heatmap');

  // Update page title
  useEffect(() => {
    const titleElement = document.getElementById('page-title');
    if (titleElement) {
      titleElement.textContent = 'Dashboard';
    }
  }, []);

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {statsCards.map((card, index) => (
          <Card
            key={index}
            className="bg-card text-card-foreground overflow-hidden dark:border-gray-800"
          >
            <CardHeader className="pb-2 pt-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {card.title}
                </CardTitle>
                {card.icon}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-green-500">{card.change}</span>{' '}
                {card.period}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex items-center justify-between">
        <div className="text-lg font-medium">QR Scan Locations</div>
        <div className="flex space-x-2">
          <Button
            variant={viewType === 'heatmap' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewType('heatmap')}
          >
            Map View
          </Button>
          <Button
            variant={viewType === 'list-view' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewType('list-view')}
          >
            List View
          </Button>
        </div>
      </div>

      {viewType === 'list-view' ? (
        <PaginatedDataTable data={locationData} itemsPerPage={7} />
      ) : (
        <div className="bg-gray-100 rounded-md shadow-sm">
          <div className="h-96 relative" id="map-container">
            <WorldMap locationData={locationData} />
          </div>
        </div>
      )}
    </div>
  );
}
