'use client';

import { useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Mock product data
const products = [
  {
    id: 1,
    name: 'Premium Widget A',
    sku: 'PWA-001',
    category: 'Electronics',
    qrCodes: 1250,
    status: 'Active',
  },
  {
    id: 2,
    name: 'Standard Widget B',
    sku: 'SWB-002',
    category: 'Electronics',
    qrCodes: 850,
    status: 'Active',
  },
  {
    id: 3,
    name: 'Deluxe Widget C',
    sku: 'DWC-003',
    category: 'Premium',
    qrCodes: 2100,
    status: 'Active',
  },
  {
    id: 4,
    name: 'Basic Widget D',
    sku: 'BWD-004',
    category: 'Basic',
    qrCodes: 450,
    status: 'Inactive',
  },
];

export default function ProductCatalogue() {
  // Update page title
  useEffect(() => {
    const titleElement = document.getElementById('page-title');
    if (titleElement) {
      titleElement.textContent = 'Product Catalogue';
    }
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Product Catalogue</h2>
        <Button size="sm">Add Product</Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Products</CardTitle>
          <CardDescription>
            Manage your product catalogue and QR code assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">SKU</TableHead>
                <TableHead>Product Name</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>QR Codes</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {products.map((product) => (
                <TableRow key={product.id}>
                  <TableCell className="font-medium">{product.sku}</TableCell>
                  <TableCell>{product.name}</TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell>{product.qrCodes.toLocaleString()}</TableCell>
                  <TableCell>
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        product.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {product.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        QR Codes
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
