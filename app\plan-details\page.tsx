'use client';

import { useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle } from 'lucide-react';

const planFeatures = [
  { name: 'QR Code Generation', included: true },
  { name: 'Real-time Monitoring', included: true },
  { name: 'Global Heatmap', included: true },
  { name: 'Basic Analytics', included: true },
  { name: 'Email Alerts', included: true },
  { name: 'Advanced Analytics', included: false },
  { name: 'Custom Branding', included: false },
  { name: 'API Access', included: false },
  { name: 'Priority Support', included: false },
];

export default function PlanDetails() {
  // Update page title
  useEffect(() => {
    const titleElement = document.getElementById('page-title');
    if (titleElement) {
      titleElement.textContent = 'Plan Details';
    }
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Plan Details</h2>
        <Button size="sm">Upgrade Plan</Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Current Plan
              <Badge variant="secondary">Enterprise</Badge>
            </CardTitle>
            <CardDescription>
              Your current subscription details and usage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Plan Type:</span>
                <span className="font-medium">Enterprise</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Monthly Cost:</span>
                <span className="font-medium">$299/month</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">QR Codes Used:</span>
                <span className="font-medium">14,568 / 50,000</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Renewal Date:</span>
                <span className="font-medium">August 15, 2025</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Status:</span>
                <Badge variant="default">Active</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Plan Features</CardTitle>
            <CardDescription>
              Features included in your current plan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {planFeatures.map((feature, index) => (
                <div key={index} className="flex items-center space-x-3">
                  {feature.included ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-gray-400" />
                  )}
                  <span
                    className={
                      feature.included
                        ? 'text-foreground'
                        : 'text-muted-foreground'
                    }
                  >
                    {feature.name}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Usage Statistics</CardTitle>
          <CardDescription>
            Your usage statistics for the current billing period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">14,568</div>
              <div className="text-sm text-muted-foreground">QR Codes Generated</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">2,847</div>
              <div className="text-sm text-muted-foreground">Scans This Month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">99.97%</div>
              <div className="text-sm text-muted-foreground">Uptime</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
