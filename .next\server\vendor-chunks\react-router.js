"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-router";
exports.ids = ["vendor-chunks/react-router"];
exports.modules = {

/***/ "(rsc)/./node_modules/react-router/dist/index.js":
/*!*************************************************!*\
  !*** ./node_modules/react-router/dist/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AbortedDeferredError: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.AbortedDeferredError),\n/* harmony export */   Await: () => (/* binding */ Await),\n/* harmony export */   MemoryRouter: () => (/* binding */ MemoryRouter),\n/* harmony export */   Navigate: () => (/* binding */ Navigate),\n/* harmony export */   NavigationType: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.Action),\n/* harmony export */   Outlet: () => (/* binding */ Outlet),\n/* harmony export */   Route: () => (/* binding */ Route),\n/* harmony export */   Router: () => (/* binding */ Router),\n/* harmony export */   RouterProvider: () => (/* binding */ RouterProvider),\n/* harmony export */   Routes: () => (/* binding */ Routes),\n/* harmony export */   UNSAFE_DataRouterContext: () => (/* binding */ DataRouterContext),\n/* harmony export */   UNSAFE_DataRouterStateContext: () => (/* binding */ DataRouterStateContext),\n/* harmony export */   UNSAFE_LocationContext: () => (/* binding */ LocationContext),\n/* harmony export */   UNSAFE_NavigationContext: () => (/* binding */ NavigationContext),\n/* harmony export */   UNSAFE_RouteContext: () => (/* binding */ RouteContext),\n/* harmony export */   UNSAFE_logV6DeprecationWarnings: () => (/* binding */ logV6DeprecationWarnings),\n/* harmony export */   UNSAFE_mapRouteProperties: () => (/* binding */ mapRouteProperties),\n/* harmony export */   UNSAFE_useRouteId: () => (/* binding */ useRouteId),\n/* harmony export */   UNSAFE_useRoutesImpl: () => (/* binding */ useRoutesImpl),\n/* harmony export */   createMemoryRouter: () => (/* binding */ createMemoryRouter),\n/* harmony export */   createPath: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.createPath),\n/* harmony export */   createRoutesFromChildren: () => (/* binding */ createRoutesFromChildren),\n/* harmony export */   createRoutesFromElements: () => (/* binding */ createRoutesFromChildren),\n/* harmony export */   defer: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.defer),\n/* harmony export */   generatePath: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.generatePath),\n/* harmony export */   isRouteErrorResponse: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.isRouteErrorResponse),\n/* harmony export */   json: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.json),\n/* harmony export */   matchPath: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.matchPath),\n/* harmony export */   matchRoutes: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.matchRoutes),\n/* harmony export */   parsePath: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.parsePath),\n/* harmony export */   redirect: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.redirect),\n/* harmony export */   redirectDocument: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.redirectDocument),\n/* harmony export */   renderMatches: () => (/* binding */ renderMatches),\n/* harmony export */   replace: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.replace),\n/* harmony export */   resolvePath: () => (/* reexport safe */ _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.resolvePath),\n/* harmony export */   useActionData: () => (/* binding */ useActionData),\n/* harmony export */   useAsyncError: () => (/* binding */ useAsyncError),\n/* harmony export */   useAsyncValue: () => (/* binding */ useAsyncValue),\n/* harmony export */   useBlocker: () => (/* binding */ useBlocker),\n/* harmony export */   useHref: () => (/* binding */ useHref),\n/* harmony export */   useInRouterContext: () => (/* binding */ useInRouterContext),\n/* harmony export */   useLoaderData: () => (/* binding */ useLoaderData),\n/* harmony export */   useLocation: () => (/* binding */ useLocation),\n/* harmony export */   useMatch: () => (/* binding */ useMatch),\n/* harmony export */   useMatches: () => (/* binding */ useMatches),\n/* harmony export */   useNavigate: () => (/* binding */ useNavigate),\n/* harmony export */   useNavigation: () => (/* binding */ useNavigation),\n/* harmony export */   useNavigationType: () => (/* binding */ useNavigationType),\n/* harmony export */   useOutlet: () => (/* binding */ useOutlet),\n/* harmony export */   useOutletContext: () => (/* binding */ useOutletContext),\n/* harmony export */   useParams: () => (/* binding */ useParams),\n/* harmony export */   useResolvedPath: () => (/* binding */ useResolvedPath),\n/* harmony export */   useRevalidator: () => (/* binding */ useRevalidator),\n/* harmony export */   useRouteError: () => (/* binding */ useRouteError),\n/* harmony export */   useRouteLoaderData: () => (/* binding */ useRouteLoaderData),\n/* harmony export */   useRoutes: () => (/* binding */ useRoutes)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _remix_run_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @remix-run/router */ \"(rsc)/./node_modules/@remix-run/router/dist/router.js\");\n/**\n * React Router v6.30.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\n\n\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\n// Create react-specific types from the agnostic types in @remix-run/router to\n// export from react-router\nconst DataRouterContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nif (true) {\n  DataRouterContext.displayName = \"DataRouter\";\n}\nconst DataRouterStateContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nif (true) {\n  DataRouterStateContext.displayName = \"DataRouterState\";\n}\nconst AwaitContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nif (true) {\n  AwaitContext.displayName = \"Await\";\n}\n\n/**\n * A Navigator is a \"location changer\"; it's how you get to different locations.\n *\n * Every history instance conforms to the Navigator interface, but the\n * distinction is useful primarily when it comes to the low-level `<Router>` API\n * where both the location and a navigator must be provided separately in order\n * to avoid \"tearing\" that may occur in a suspense-enabled app if the action\n * and/or location were to be read directly from the history instance.\n */\n\nconst NavigationContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nif (true) {\n  NavigationContext.displayName = \"Navigation\";\n}\nconst LocationContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nif (true) {\n  LocationContext.displayName = \"Location\";\n}\nconst RouteContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n  outlet: null,\n  matches: [],\n  isDataRoute: false\n});\nif (true) {\n  RouteContext.displayName = \"Route\";\n}\nconst RouteErrorContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nif (true) {\n  RouteErrorContext.displayName = \"RouteError\";\n}\n\n/**\n * Returns the full href for the given \"to\" value. This is useful for building\n * custom links that are also accessible and preserve right-click behavior.\n *\n * @see https://reactrouter.com/v6/hooks/use-href\n */\nfunction useHref(to, _temp) {\n  let {\n    relative\n  } = _temp === void 0 ? {} : _temp;\n  !useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useHref() may be used only in the context of a <Router> component.\") : 0 : void 0;\n  let {\n    basename,\n    navigator\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NavigationContext);\n  let {\n    hash,\n    pathname,\n    search\n  } = useResolvedPath(to, {\n    relative\n  });\n  let joinedPathname = pathname;\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the href.  If this is a root navigation, then just use the raw\n  // basename which allows the basename to have full control over the presence\n  // of a trailing slash on root links\n  if (basename !== \"/\") {\n    joinedPathname = pathname === \"/\" ? basename : (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.joinPaths)([basename, pathname]);\n  }\n  return navigator.createHref({\n    pathname: joinedPathname,\n    search,\n    hash\n  });\n}\n\n/**\n * Returns true if this component is a descendant of a `<Router>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-in-router-context\n */\nfunction useInRouterContext() {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useContext(LocationContext) != null;\n}\n\n/**\n * Returns the current location object, which represents the current URL in web\n * browsers.\n *\n * Note: If you're using this it may mean you're doing some of your own\n * \"routing\" in your app, and we'd like to know what your use case is. We may\n * be able to provide something higher-level to better suit your needs.\n *\n * @see https://reactrouter.com/v6/hooks/use-location\n */\nfunction useLocation() {\n  !useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useLocation() may be used only in the context of a <Router> component.\") : 0 : void 0;\n  return react__WEBPACK_IMPORTED_MODULE_0__.useContext(LocationContext).location;\n}\n\n/**\n * Returns the current navigation action which describes how the router came to\n * the current location, either by a pop, push, or replace on the history stack.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigation-type\n */\nfunction useNavigationType() {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useContext(LocationContext).navigationType;\n}\n\n/**\n * Returns a PathMatch object if the given pattern matches the current URL.\n * This is useful for components that need to know \"active\" state, e.g.\n * `<NavLink>`.\n *\n * @see https://reactrouter.com/v6/hooks/use-match\n */\nfunction useMatch(pattern) {\n  !useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useMatch() may be used only in the context of a <Router> component.\") : 0 : void 0;\n  let {\n    pathname\n  } = useLocation();\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.matchPath)(pattern, (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_decodePath)(pathname)), [pathname, pattern]);\n}\n\n/**\n * The interface for the navigate() function returned from useNavigate().\n */\n\nconst navigateEffectWarning = \"You should call navigate() in a React.useEffect(), not when \" + \"your component is first rendered.\";\n\n// Mute warnings for calls to useNavigate in SSR environments\nfunction useIsomorphicLayoutEffect(cb) {\n  let isStatic = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NavigationContext).static;\n  if (!isStatic) {\n    // We should be able to get rid of this once react 18.3 is released\n    // See: https://github.com/facebook/react/pull/26395\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(cb);\n  }\n}\n\n/**\n * Returns an imperative method for changing the location. Used by `<Link>`s, but\n * may also be used by other elements to change the location.\n *\n * @see https://reactrouter.com/v6/hooks/use-navigate\n */\nfunction useNavigate() {\n  let {\n    isDataRoute\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  // Conditional usage is OK here because the usage of a data router is static\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return isDataRoute ? useNavigateStable() : useNavigateUnstable();\n}\nfunction useNavigateUnstable() {\n  !useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useNavigate() may be used only in the context of a <Router> component.\") : 0 : void 0;\n  let dataRouterContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DataRouterContext);\n  let {\n    basename,\n    future,\n    navigator\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NavigationContext);\n  let {\n    matches\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify((0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_getResolveToMatches)(matches, future.v7_relativeSplatPath));\n  let activeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n     true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(activeRef.current, navigateEffectWarning) : 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our history listener yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      navigator.go(to);\n      return;\n    }\n    let path = (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.resolveTo)(to, JSON.parse(routePathnamesJson), locationPathname, options.relative === \"path\");\n\n    // If we're operating within a basename, prepend it to the pathname prior\n    // to handing off to history (but only if we're not in a data router,\n    // otherwise it'll prepend the basename inside of the router).\n    // If this is a root navigation, then we navigate to the raw basename\n    // which allows the basename to have full control over the presence of a\n    // trailing slash on root links\n    if (dataRouterContext == null && basename !== \"/\") {\n      path.pathname = path.pathname === \"/\" ? basename : (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.joinPaths)([basename, path.pathname]);\n    }\n    (!!options.replace ? navigator.replace : navigator.push)(path, options.state, options);\n  }, [basename, navigator, routePathnamesJson, locationPathname, dataRouterContext]);\n  return navigate;\n}\nconst OutletContext = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n\n/**\n * Returns the context (if provided) for the child route at this level of the route\n * hierarchy.\n * @see https://reactrouter.com/v6/hooks/use-outlet-context\n */\nfunction useOutletContext() {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useContext(OutletContext);\n}\n\n/**\n * Returns the element for the child route at this level of the route\n * hierarchy. Used internally by `<Outlet>` to render child routes.\n *\n * @see https://reactrouter.com/v6/hooks/use-outlet\n */\nfunction useOutlet(context) {\n  let outlet = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext).outlet;\n  if (outlet) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(OutletContext.Provider, {\n      value: context\n    }, outlet);\n  }\n  return outlet;\n}\n\n/**\n * Returns an object of key/value pairs of the dynamic params from the current\n * URL that were matched by the route path.\n *\n * @see https://reactrouter.com/v6/hooks/use-params\n */\nfunction useParams() {\n  let {\n    matches\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  let routeMatch = matches[matches.length - 1];\n  return routeMatch ? routeMatch.params : {};\n}\n\n/**\n * Resolves the pathname of the given `to` value against the current location.\n *\n * @see https://reactrouter.com/v6/hooks/use-resolved-path\n */\nfunction useResolvedPath(to, _temp2) {\n  let {\n    relative\n  } = _temp2 === void 0 ? {} : _temp2;\n  let {\n    future\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NavigationContext);\n  let {\n    matches\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let routePathnamesJson = JSON.stringify((0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_getResolveToMatches)(matches, future.v7_relativeSplatPath));\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.resolveTo)(to, JSON.parse(routePathnamesJson), locationPathname, relative === \"path\"), [to, routePathnamesJson, locationPathname, relative]);\n}\n\n/**\n * Returns the element of the route that matched the current location, prepared\n * with the correct context to render the remainder of the route tree. Route\n * elements in the tree must render an `<Outlet>` to render their child route's\n * element.\n *\n * @see https://reactrouter.com/v6/hooks/use-routes\n */\nfunction useRoutes(routes, locationArg) {\n  return useRoutesImpl(routes, locationArg);\n}\n\n// Internal implementation with accept optional param for RouterProvider usage\nfunction useRoutesImpl(routes, locationArg, dataRouterState, future) {\n  !useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, // TODO: This error is probably because they somehow have 2 versions of the\n  // router loaded. We can help them understand how to avoid that.\n  \"useRoutes() may be used only in the context of a <Router> component.\") : 0 : void 0;\n  let {\n    navigator,\n    static: isStatic\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NavigationContext);\n  let {\n    matches: parentMatches\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  let routeMatch = parentMatches[parentMatches.length - 1];\n  let parentParams = routeMatch ? routeMatch.params : {};\n  let parentPathname = routeMatch ? routeMatch.pathname : \"/\";\n  let parentPathnameBase = routeMatch ? routeMatch.pathnameBase : \"/\";\n  let parentRoute = routeMatch && routeMatch.route;\n  if (true) {\n    // You won't get a warning about 2 different <Routes> under a <Route>\n    // without a trailing *, but this is a best-effort warning anyway since we\n    // cannot even give the warning unless they land at the parent route.\n    //\n    // Example:\n    //\n    // <Routes>\n    //   {/* This route path MUST end with /* because otherwise\n    //       it will never match /blog/post/123 */}\n    //   <Route path=\"blog\" element={<Blog />} />\n    //   <Route path=\"blog/feed\" element={<BlogFeed />} />\n    // </Routes>\n    //\n    // function Blog() {\n    //   return (\n    //     <Routes>\n    //       <Route path=\"post/:id\" element={<Post />} />\n    //     </Routes>\n    //   );\n    // }\n    let parentPath = parentRoute && parentRoute.path || \"\";\n    warningOnce(parentPathname, !parentRoute || parentPath.endsWith(\"*\"), \"You rendered descendant <Routes> (or called `useRoutes()`) at \" + (\"\\\"\" + parentPathname + \"\\\" (under <Route path=\\\"\" + parentPath + \"\\\">) but the \") + \"parent route path has no trailing \\\"*\\\". This means if you navigate \" + \"deeper, the parent won't match anymore and therefore the child \" + \"routes will never render.\\n\\n\" + (\"Please change the parent <Route path=\\\"\" + parentPath + \"\\\"> to <Route \") + (\"path=\\\"\" + (parentPath === \"/\" ? \"*\" : parentPath + \"/*\") + \"\\\">.\"));\n  }\n  let locationFromContext = useLocation();\n  let location;\n  if (locationArg) {\n    var _parsedLocationArg$pa;\n    let parsedLocationArg = typeof locationArg === \"string\" ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.parsePath)(locationArg) : locationArg;\n    !(parentPathnameBase === \"/\" || ((_parsedLocationArg$pa = parsedLocationArg.pathname) == null ? void 0 : _parsedLocationArg$pa.startsWith(parentPathnameBase))) ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, \"When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, \" + \"the location pathname must begin with the portion of the URL pathname that was \" + (\"matched by all parent routes. The current pathname base is \\\"\" + parentPathnameBase + \"\\\" \") + (\"but pathname \\\"\" + parsedLocationArg.pathname + \"\\\" was given in the `location` prop.\")) : 0 : void 0;\n    location = parsedLocationArg;\n  } else {\n    location = locationFromContext;\n  }\n  let pathname = location.pathname || \"/\";\n  let remainingPathname = pathname;\n  if (parentPathnameBase !== \"/\") {\n    // Determine the remaining pathname by removing the # of URL segments the\n    // parentPathnameBase has, instead of removing based on character count.\n    // This is because we can't guarantee that incoming/outgoing encodings/\n    // decodings will match exactly.\n    // We decode paths before matching on a per-segment basis with\n    // decodeURIComponent(), but we re-encode pathnames via `new URL()` so they\n    // match what `window.location.pathname` would reflect.  Those don't 100%\n    // align when it comes to encoded URI characters such as % and &.\n    //\n    // So we may end up with:\n    //   pathname:           \"/descendant/a%25b/match\"\n    //   parentPathnameBase: \"/descendant/a%b\"\n    //\n    // And the direct substring removal approach won't work :/\n    let parentSegments = parentPathnameBase.replace(/^\\//, \"\").split(\"/\");\n    let segments = pathname.replace(/^\\//, \"\").split(\"/\");\n    remainingPathname = \"/\" + segments.slice(parentSegments.length).join(\"/\");\n  }\n  let matches = !isStatic && dataRouterState && dataRouterState.matches && dataRouterState.matches.length > 0 ? dataRouterState.matches : (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.matchRoutes)(routes, {\n    pathname: remainingPathname\n  });\n  if (true) {\n     true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(parentRoute || matches != null, \"No routes matched location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \") : 0;\n     true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(matches == null || matches[matches.length - 1].route.element !== undefined || matches[matches.length - 1].route.Component !== undefined || matches[matches.length - 1].route.lazy !== undefined, \"Matched leaf route at location \\\"\" + location.pathname + location.search + location.hash + \"\\\" \" + \"does not have an element or Component. This means it will render an <Outlet /> with a \" + \"null value by default resulting in an \\\"empty\\\" page.\") : 0;\n  }\n  let renderedMatches = _renderMatches(matches && matches.map(match => Object.assign({}, match, {\n    params: Object.assign({}, parentParams, match.params),\n    pathname: (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.joinPaths)([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathname).pathname : match.pathname]),\n    pathnameBase: match.pathnameBase === \"/\" ? parentPathnameBase : (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.joinPaths)([parentPathnameBase,\n    // Re-encode pathnames that were decoded inside matchRoutes\n    navigator.encodeLocation ? navigator.encodeLocation(match.pathnameBase).pathname : match.pathnameBase])\n  })), parentMatches, dataRouterState, future);\n\n  // When a user passes in a `locationArg`, the associated routes need to\n  // be wrapped in a new `LocationContext.Provider` in order for `useLocation`\n  // to use the scoped location instead of the global location.\n  if (locationArg && renderedMatches) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(LocationContext.Provider, {\n      value: {\n        location: _extends({\n          pathname: \"/\",\n          search: \"\",\n          hash: \"\",\n          state: null,\n          key: \"default\"\n        }, location),\n        navigationType: _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.Action.Pop\n      }\n    }, renderedMatches);\n  }\n  return renderedMatches;\n}\nfunction DefaultErrorComponent() {\n  let error = useRouteError();\n  let message = (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.isRouteErrorResponse)(error) ? error.status + \" \" + error.statusText : error instanceof Error ? error.message : JSON.stringify(error);\n  let stack = error instanceof Error ? error.stack : null;\n  let lightgrey = \"rgba(200,200,200, 0.5)\";\n  let preStyles = {\n    padding: \"0.5rem\",\n    backgroundColor: lightgrey\n  };\n  let codeStyles = {\n    padding: \"2px 4px\",\n    backgroundColor: lightgrey\n  };\n  let devInfo = null;\n  if (true) {\n    console.error(\"Error handled by React Router default ErrorBoundary:\", error);\n    devInfo = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"p\", null, \"\\uD83D\\uDCBF Hey developer \\uD83D\\uDC4B\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"p\", null, \"You can provide a way better UX than this when your app throws errors by providing your own \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"code\", {\n      style: codeStyles\n    }, \"ErrorBoundary\"), \" or\", \" \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"code\", {\n      style: codeStyles\n    }, \"errorElement\"), \" prop on your route.\"));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"h2\", null, \"Unexpected Application Error!\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"h3\", {\n    style: {\n      fontStyle: \"italic\"\n    }\n  }, message), stack ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"pre\", {\n    style: preStyles\n  }, stack) : null, devInfo);\n}\nconst defaultErrorElement = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(DefaultErrorComponent, null);\nclass RenderErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      location: props.location,\n      revalidation: props.revalidation,\n      error: props.error\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    // When we get into an error state, the user will likely click \"back\" to the\n    // previous page that didn't have an error. Because this wraps the entire\n    // application, that will have no effect--the error page continues to display.\n    // This gives us a mechanism to recover from the error when the location changes.\n    //\n    // Whether we're in an error state or not, we update the location in state\n    // so that when we are in an error state, it gets reset when a new location\n    // comes in and the user recovers from the error.\n    if (state.location !== props.location || state.revalidation !== \"idle\" && props.revalidation === \"idle\") {\n      return {\n        error: props.error,\n        location: props.location,\n        revalidation: props.revalidation\n      };\n    }\n\n    // If we're not changing locations, preserve the location but still surface\n    // any new errors that may come through. We retain the existing error, we do\n    // this because the error provided from the app state may be cleared without\n    // the location changing.\n    return {\n      error: props.error !== undefined ? props.error : state.error,\n      location: state.location,\n      revalidation: props.revalidation || state.revalidation\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"React Router caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    return this.state.error !== undefined ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RouteContext.Provider, {\n      value: this.props.routeContext\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RouteErrorContext.Provider, {\n      value: this.state.error,\n      children: this.props.component\n    })) : this.props.children;\n  }\n}\nfunction RenderedRoute(_ref) {\n  let {\n    routeContext,\n    match,\n    children\n  } = _ref;\n  let dataRouterContext = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DataRouterContext);\n\n  // Track how deep we got in our render pass to emulate SSR componentDidCatch\n  // in a DataStaticRouter\n  if (dataRouterContext && dataRouterContext.static && dataRouterContext.staticContext && (match.route.errorElement || match.route.ErrorBoundary)) {\n    dataRouterContext.staticContext._deepestRenderedBoundaryId = match.route.id;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RouteContext.Provider, {\n    value: routeContext\n  }, children);\n}\nfunction _renderMatches(matches, parentMatches, dataRouterState, future) {\n  var _dataRouterState;\n  if (parentMatches === void 0) {\n    parentMatches = [];\n  }\n  if (dataRouterState === void 0) {\n    dataRouterState = null;\n  }\n  if (future === void 0) {\n    future = null;\n  }\n  if (matches == null) {\n    var _future;\n    if (!dataRouterState) {\n      return null;\n    }\n    if (dataRouterState.errors) {\n      // Don't bail if we have data router errors so we can render them in the\n      // boundary.  Use the pre-matched (or shimmed) matches\n      matches = dataRouterState.matches;\n    } else if ((_future = future) != null && _future.v7_partialHydration && parentMatches.length === 0 && !dataRouterState.initialized && dataRouterState.matches.length > 0) {\n      // Don't bail if we're initializing with partial hydration and we have\n      // router matches.  That means we're actively running `patchRoutesOnNavigation`\n      // so we should render down the partial matches to the appropriate\n      // `HydrateFallback`.  We only do this if `parentMatches` is empty so it\n      // only impacts the root matches for `RouterProvider` and no descendant\n      // `<Routes>`\n      matches = dataRouterState.matches;\n    } else {\n      return null;\n    }\n  }\n  let renderedMatches = matches;\n\n  // If we have data errors, trim matches to the highest error boundary\n  let errors = (_dataRouterState = dataRouterState) == null ? void 0 : _dataRouterState.errors;\n  if (errors != null) {\n    let errorIndex = renderedMatches.findIndex(m => m.route.id && (errors == null ? void 0 : errors[m.route.id]) !== undefined);\n    !(errorIndex >= 0) ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, \"Could not find a matching route for errors on route IDs: \" + Object.keys(errors).join(\",\")) : 0 : void 0;\n    renderedMatches = renderedMatches.slice(0, Math.min(renderedMatches.length, errorIndex + 1));\n  }\n\n  // If we're in a partial hydration mode, detect if we need to render down to\n  // a given HydrateFallback while we load the rest of the hydration data\n  let renderFallback = false;\n  let fallbackIndex = -1;\n  if (dataRouterState && future && future.v7_partialHydration) {\n    for (let i = 0; i < renderedMatches.length; i++) {\n      let match = renderedMatches[i];\n      // Track the deepest fallback up until the first route without data\n      if (match.route.HydrateFallback || match.route.hydrateFallbackElement) {\n        fallbackIndex = i;\n      }\n      if (match.route.id) {\n        let {\n          loaderData,\n          errors\n        } = dataRouterState;\n        let needsToRunLoader = match.route.loader && loaderData[match.route.id] === undefined && (!errors || errors[match.route.id] === undefined);\n        if (match.route.lazy || needsToRunLoader) {\n          // We found the first route that's not ready to render (waiting on\n          // lazy, or has a loader that hasn't run yet).  Flag that we need to\n          // render a fallback and render up until the appropriate fallback\n          renderFallback = true;\n          if (fallbackIndex >= 0) {\n            renderedMatches = renderedMatches.slice(0, fallbackIndex + 1);\n          } else {\n            renderedMatches = [renderedMatches[0]];\n          }\n          break;\n        }\n      }\n    }\n  }\n  return renderedMatches.reduceRight((outlet, match, index) => {\n    // Only data routers handle errors/fallbacks\n    let error;\n    let shouldRenderHydrateFallback = false;\n    let errorElement = null;\n    let hydrateFallbackElement = null;\n    if (dataRouterState) {\n      error = errors && match.route.id ? errors[match.route.id] : undefined;\n      errorElement = match.route.errorElement || defaultErrorElement;\n      if (renderFallback) {\n        if (fallbackIndex < 0 && index === 0) {\n          warningOnce(\"route-fallback\", false, \"No `HydrateFallback` element provided to render during initial hydration\");\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = null;\n        } else if (fallbackIndex === index) {\n          shouldRenderHydrateFallback = true;\n          hydrateFallbackElement = match.route.hydrateFallbackElement || null;\n        }\n      }\n    }\n    let matches = parentMatches.concat(renderedMatches.slice(0, index + 1));\n    let getChildren = () => {\n      let children;\n      if (error) {\n        children = errorElement;\n      } else if (shouldRenderHydrateFallback) {\n        children = hydrateFallbackElement;\n      } else if (match.route.Component) {\n        // Note: This is a de-optimized path since React won't re-use the\n        // ReactElement since it's identity changes with each new\n        // React.createElement call.  We keep this so folks can use\n        // `<Route Component={...}>` in `<Routes>` but generally `Component`\n        // usage is only advised in `RouterProvider` when we can convert it to\n        // `element` ahead of time.\n        children = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(match.route.Component, null);\n      } else if (match.route.element) {\n        children = match.route.element;\n      } else {\n        children = outlet;\n      }\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RenderedRoute, {\n        match: match,\n        routeContext: {\n          outlet,\n          matches,\n          isDataRoute: dataRouterState != null\n        },\n        children: children\n      });\n    };\n    // Only wrap in an error boundary within data router usages when we have an\n    // ErrorBoundary/errorElement on this route.  Otherwise let it bubble up to\n    // an ancestor ErrorBoundary/errorElement\n    return dataRouterState && (match.route.ErrorBoundary || match.route.errorElement || index === 0) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(RenderErrorBoundary, {\n      location: dataRouterState.location,\n      revalidation: dataRouterState.revalidation,\n      component: errorElement,\n      error: error,\n      children: getChildren(),\n      routeContext: {\n        outlet: null,\n        matches,\n        isDataRoute: true\n      }\n    }) : getChildren();\n  }, null);\n}\nvar DataRouterHook = /*#__PURE__*/function (DataRouterHook) {\n  DataRouterHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterHook[\"UseNavigateStable\"] = \"useNavigate\";\n  return DataRouterHook;\n}(DataRouterHook || {});\nvar DataRouterStateHook = /*#__PURE__*/function (DataRouterStateHook) {\n  DataRouterStateHook[\"UseBlocker\"] = \"useBlocker\";\n  DataRouterStateHook[\"UseLoaderData\"] = \"useLoaderData\";\n  DataRouterStateHook[\"UseActionData\"] = \"useActionData\";\n  DataRouterStateHook[\"UseRouteError\"] = \"useRouteError\";\n  DataRouterStateHook[\"UseNavigation\"] = \"useNavigation\";\n  DataRouterStateHook[\"UseRouteLoaderData\"] = \"useRouteLoaderData\";\n  DataRouterStateHook[\"UseMatches\"] = \"useMatches\";\n  DataRouterStateHook[\"UseRevalidator\"] = \"useRevalidator\";\n  DataRouterStateHook[\"UseNavigateStable\"] = \"useNavigate\";\n  DataRouterStateHook[\"UseRouteId\"] = \"useRouteId\";\n  return DataRouterStateHook;\n}(DataRouterStateHook || {});\nfunction getDataRouterConsoleError(hookName) {\n  return hookName + \" must be used within a data router.  See https://reactrouter.com/v6/routers/picking-a-router.\";\n}\nfunction useDataRouterContext(hookName) {\n  let ctx = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DataRouterContext);\n  !ctx ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, getDataRouterConsoleError(hookName)) : 0 : void 0;\n  return ctx;\n}\nfunction useDataRouterState(hookName) {\n  let state = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DataRouterStateContext);\n  !state ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, getDataRouterConsoleError(hookName)) : 0 : void 0;\n  return state;\n}\nfunction useRouteContext(hookName) {\n  let route = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  !route ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, getDataRouterConsoleError(hookName)) : 0 : void 0;\n  return route;\n}\n\n// Internal version with hookName-aware debugging\nfunction useCurrentRouteId(hookName) {\n  let route = useRouteContext(hookName);\n  let thisRoute = route.matches[route.matches.length - 1];\n  !thisRoute.route.id ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, hookName + \" can only be used on routes that contain a unique \\\"id\\\"\") : 0 : void 0;\n  return thisRoute.route.id;\n}\n\n/**\n * Returns the ID for the nearest contextual route\n */\nfunction useRouteId() {\n  return useCurrentRouteId(DataRouterStateHook.UseRouteId);\n}\n\n/**\n * Returns the current navigation, defaulting to an \"idle\" navigation when\n * no navigation is in progress\n */\nfunction useNavigation() {\n  let state = useDataRouterState(DataRouterStateHook.UseNavigation);\n  return state.navigation;\n}\n\n/**\n * Returns a revalidate function for manually triggering revalidation, as well\n * as the current state of any manual revalidations\n */\nfunction useRevalidator() {\n  let dataRouterContext = useDataRouterContext(DataRouterHook.UseRevalidator);\n  let state = useDataRouterState(DataRouterStateHook.UseRevalidator);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    revalidate: dataRouterContext.router.revalidate,\n    state: state.revalidation\n  }), [dataRouterContext.router.revalidate, state.revalidation]);\n}\n\n/**\n * Returns the active route matches, useful for accessing loaderData for\n * parent/child routes or the route \"handle\" property\n */\nfunction useMatches() {\n  let {\n    matches,\n    loaderData\n  } = useDataRouterState(DataRouterStateHook.UseMatches);\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => matches.map(m => (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_convertRouteMatchToUiMatch)(m, loaderData)), [matches, loaderData]);\n}\n\n/**\n * Returns the loader data for the nearest ancestor Route loader\n */\nfunction useLoaderData() {\n  let state = useDataRouterState(DataRouterStateHook.UseLoaderData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  if (state.errors && state.errors[routeId] != null) {\n    console.error(\"You cannot `useLoaderData` in an errorElement (routeId: \" + routeId + \")\");\n    return undefined;\n  }\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the loaderData for the given routeId\n */\nfunction useRouteLoaderData(routeId) {\n  let state = useDataRouterState(DataRouterStateHook.UseRouteLoaderData);\n  return state.loaderData[routeId];\n}\n\n/**\n * Returns the action data for the nearest ancestor Route action\n */\nfunction useActionData() {\n  let state = useDataRouterState(DataRouterStateHook.UseActionData);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseLoaderData);\n  return state.actionData ? state.actionData[routeId] : undefined;\n}\n\n/**\n * Returns the nearest ancestor Route error, which could be a loader/action\n * error or a render error.  This is intended to be called from your\n * ErrorBoundary/errorElement to display a proper error message.\n */\nfunction useRouteError() {\n  var _state$errors;\n  let error = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteErrorContext);\n  let state = useDataRouterState(DataRouterStateHook.UseRouteError);\n  let routeId = useCurrentRouteId(DataRouterStateHook.UseRouteError);\n\n  // If this was a render error, we put it in a RouteError context inside\n  // of RenderErrorBoundary\n  if (error !== undefined) {\n    return error;\n  }\n\n  // Otherwise look for errors from our data router state\n  return (_state$errors = state.errors) == null ? void 0 : _state$errors[routeId];\n}\n\n/**\n * Returns the happy-path data from the nearest ancestor `<Await />` value\n */\nfunction useAsyncValue() {\n  let value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(AwaitContext);\n  return value == null ? void 0 : value._data;\n}\n\n/**\n * Returns the error from the nearest ancestor `<Await />` value\n */\nfunction useAsyncError() {\n  let value = react__WEBPACK_IMPORTED_MODULE_0__.useContext(AwaitContext);\n  return value == null ? void 0 : value._error;\n}\nlet blockerId = 0;\n\n/**\n * Allow the application to block navigations within the SPA and present the\n * user a confirmation dialog to confirm the navigation.  Mostly used to avoid\n * using half-filled form data.  This does not handle hard-reloads or\n * cross-origin navigations.\n */\nfunction useBlocker(shouldBlock) {\n  let {\n    router,\n    basename\n  } = useDataRouterContext(DataRouterHook.UseBlocker);\n  let state = useDataRouterState(DataRouterStateHook.UseBlocker);\n  let [blockerKey, setBlockerKey] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n  let blockerFunction = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(arg => {\n    if (typeof shouldBlock !== \"function\") {\n      return !!shouldBlock;\n    }\n    if (basename === \"/\") {\n      return shouldBlock(arg);\n    }\n\n    // If they provided us a function and we've got an active basename, strip\n    // it from the locations we expose to the user to match the behavior of\n    // useLocation\n    let {\n      currentLocation,\n      nextLocation,\n      historyAction\n    } = arg;\n    return shouldBlock({\n      currentLocation: _extends({}, currentLocation, {\n        pathname: (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.stripBasename)(currentLocation.pathname, basename) || currentLocation.pathname\n      }),\n      nextLocation: _extends({}, nextLocation, {\n        pathname: (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.stripBasename)(nextLocation.pathname, basename) || nextLocation.pathname\n      }),\n      historyAction\n    });\n  }, [basename, shouldBlock]);\n\n  // This effect is in charge of blocker key assignment and deletion (which is\n  // tightly coupled to the key)\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    let key = String(++blockerId);\n    setBlockerKey(key);\n    return () => router.deleteBlocker(key);\n  }, [router]);\n\n  // This effect handles assigning the blockerFunction.  This is to handle\n  // unstable blocker function identities, and happens only after the prior\n  // effect so we don't get an orphaned blockerFunction in the router with a\n  // key of \"\".  Until then we just have the IDLE_BLOCKER.\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (blockerKey !== \"\") {\n      router.getBlocker(blockerKey, blockerFunction);\n    }\n  }, [router, blockerKey, blockerFunction]);\n\n  // Prefer the blocker from `state` not `router.state` since DataRouterContext\n  // is memoized so this ensures we update on blocker state updates\n  return blockerKey && state.blockers.has(blockerKey) ? state.blockers.get(blockerKey) : _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.IDLE_BLOCKER;\n}\n\n/**\n * Stable version of useNavigate that is used when we are in the context of\n * a RouterProvider.\n */\nfunction useNavigateStable() {\n  let {\n    router\n  } = useDataRouterContext(DataRouterHook.UseNavigateStable);\n  let id = useCurrentRouteId(DataRouterStateHook.UseNavigateStable);\n  let activeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  useIsomorphicLayoutEffect(() => {\n    activeRef.current = true;\n  });\n  let navigate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (to, options) {\n    if (options === void 0) {\n      options = {};\n    }\n     true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(activeRef.current, navigateEffectWarning) : 0;\n\n    // Short circuit here since if this happens on first render the navigate\n    // is useless because we haven't wired up our router subscriber yet\n    if (!activeRef.current) return;\n    if (typeof to === \"number\") {\n      router.navigate(to);\n    } else {\n      router.navigate(to, _extends({\n        fromRouteId: id\n      }, options));\n    }\n  }, [router, id]);\n  return navigate;\n}\nconst alreadyWarned$1 = {};\nfunction warningOnce(key, cond, message) {\n  if (!cond && !alreadyWarned$1[key]) {\n    alreadyWarned$1[key] = true;\n     true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(false, message) : 0;\n  }\n}\n\nconst alreadyWarned = {};\nfunction warnOnce(key, message) {\n  if ( true && !alreadyWarned[message]) {\n    alreadyWarned[message] = true;\n    console.warn(message);\n  }\n}\nconst logDeprecation = (flag, msg, link) => warnOnce(flag, \"\\u26A0\\uFE0F React Router Future Flag Warning: \" + msg + \". \" + (\"You can use the `\" + flag + \"` future flag to opt-in early. \") + (\"For more information, see \" + link + \".\"));\nfunction logV6DeprecationWarnings(renderFuture, routerFuture) {\n  if ((renderFuture == null ? void 0 : renderFuture.v7_startTransition) === undefined) {\n    logDeprecation(\"v7_startTransition\", \"React Router will begin wrapping state updates in `React.startTransition` in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_starttransition\");\n  }\n  if ((renderFuture == null ? void 0 : renderFuture.v7_relativeSplatPath) === undefined && (!routerFuture || !routerFuture.v7_relativeSplatPath)) {\n    logDeprecation(\"v7_relativeSplatPath\", \"Relative route resolution within Splat routes is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath\");\n  }\n  if (routerFuture) {\n    if (routerFuture.v7_fetcherPersist === undefined) {\n      logDeprecation(\"v7_fetcherPersist\", \"The persistence behavior of fetchers is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_fetcherpersist\");\n    }\n    if (routerFuture.v7_normalizeFormMethod === undefined) {\n      logDeprecation(\"v7_normalizeFormMethod\", \"Casing of `formMethod` fields is being normalized to uppercase in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_normalizeformmethod\");\n    }\n    if (routerFuture.v7_partialHydration === undefined) {\n      logDeprecation(\"v7_partialHydration\", \"`RouterProvider` hydration behavior is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_partialhydration\");\n    }\n    if (routerFuture.v7_skipActionErrorRevalidation === undefined) {\n      logDeprecation(\"v7_skipActionErrorRevalidation\", \"The revalidation behavior after 4xx/5xx `action` responses is changing in v7\", \"https://reactrouter.com/v6/upgrading/future#v7_skipactionerrorrevalidation\");\n    }\n  }\n}\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = react__WEBPACK_IMPORTED_MODULE_0__[START_TRANSITION];\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nfunction RouterProvider(_ref) {\n  let {\n    fallbackElement,\n    router,\n    future\n  } = _ref;\n  let [state, setStateImpl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(router.state);\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(newState => {\n    if (v7_startTransition && startTransitionImpl) {\n      startTransitionImpl(() => setStateImpl(newState));\n    } else {\n      setStateImpl(newState);\n    }\n  }, [setStateImpl, v7_startTransition]);\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n     true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(fallbackElement == null || !router.future.v7_partialHydration, \"`<RouterProvider fallbackElement>` is deprecated when using \" + \"`v7_partialHydration`, use a `HydrateFallback` component instead\") : 0;\n    // Only log this once on initial mount\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  let navigator = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: n => router.navigate(n),\n      push: (to, state, opts) => router.navigate(to, {\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      }),\n      replace: (to, state, opts) => router.navigate(to, {\n        replace: true,\n        state,\n        preventScrollReset: opts == null ? void 0 : opts.preventScrollReset\n      })\n    };\n  }, [router]);\n  let basename = router.basename || \"/\";\n  let dataRouterContext = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    router,\n    navigator,\n    static: false,\n    basename\n  }), [router, navigator, basename]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => logV6DeprecationWarnings(future, router.future), [router, future]);\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(DataRouterContext.Provider, {\n    value: dataRouterContext\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(DataRouterStateContext.Provider, {\n    value: state\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Router, {\n    basename: basename,\n    location: state.location,\n    navigationType: state.historyAction,\n    navigator: navigator,\n    future: {\n      v7_relativeSplatPath: router.future.v7_relativeSplatPath\n    }\n  }, state.initialized || router.future.v7_partialHydration ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(DataRoutes, {\n    routes: router.routes,\n    future: router.future,\n    state: state\n  }) : fallbackElement))), null);\n}\nfunction DataRoutes(_ref2) {\n  let {\n    routes,\n    future,\n    state\n  } = _ref2;\n  return useRoutesImpl(routes, undefined, state, future);\n}\n/**\n * A `<Router>` that stores all entries in memory.\n *\n * @see https://reactrouter.com/v6/router-components/memory-router\n */\nfunction MemoryRouter(_ref3) {\n  let {\n    basename,\n    children,\n    initialEntries,\n    initialIndex,\n    future\n  } = _ref3;\n  let historyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  if (historyRef.current == null) {\n    historyRef.current = (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.createMemoryHistory)({\n      initialEntries,\n      initialIndex,\n      v5Compat: true\n    });\n  }\n  let history = historyRef.current;\n  let [state, setStateImpl] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n    action: history.action,\n    location: history.location\n  });\n  let {\n    v7_startTransition\n  } = future || {};\n  let setState = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(newState => {\n    v7_startTransition && startTransitionImpl ? startTransitionImpl(() => setStateImpl(newState)) : setStateImpl(newState);\n  }, [setStateImpl, v7_startTransition]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => history.listen(setState), [history, setState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => logV6DeprecationWarnings(future), [future]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(Router, {\n    basename: basename,\n    children: children,\n    location: state.location,\n    navigationType: state.action,\n    navigator: history,\n    future: future\n  });\n}\n/**\n * Changes the current location.\n *\n * Note: This API is mostly useful in React.Component subclasses that are not\n * able to use hooks. In functional components, we recommend you use the\n * `useNavigate` hook instead.\n *\n * @see https://reactrouter.com/v6/components/navigate\n */\nfunction Navigate(_ref4) {\n  let {\n    to,\n    replace,\n    state,\n    relative\n  } = _ref4;\n  !useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, // TODO: This error is probably because they somehow have 2 versions of\n  // the router loaded. We can help them understand how to avoid that.\n  \"<Navigate> may be used only in the context of a <Router> component.\") : 0 : void 0;\n  let {\n    future,\n    static: isStatic\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(NavigationContext);\n   true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(!isStatic, \"<Navigate> must not be used on the initial render in a <StaticRouter>. \" + \"This is a no-op, but you should modify your code so the <Navigate> is \" + \"only ever rendered in response to some user interaction or state change.\") : 0;\n  let {\n    matches\n  } = react__WEBPACK_IMPORTED_MODULE_0__.useContext(RouteContext);\n  let {\n    pathname: locationPathname\n  } = useLocation();\n  let navigate = useNavigate();\n\n  // Resolve the path outside of the effect so that when effects run twice in\n  // StrictMode they navigate to the same place\n  let path = (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.resolveTo)(to, (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_getResolveToMatches)(matches, future.v7_relativeSplatPath), locationPathname, relative === \"path\");\n  let jsonPath = JSON.stringify(path);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => navigate(JSON.parse(jsonPath), {\n    replace,\n    state,\n    relative\n  }), [navigate, jsonPath, relative, replace, state]);\n  return null;\n}\n/**\n * Renders the child route's element, if there is one.\n *\n * @see https://reactrouter.com/v6/components/outlet\n */\nfunction Outlet(props) {\n  return useOutlet(props.context);\n}\n/**\n * Declares an element that should be rendered at a certain URL path.\n *\n * @see https://reactrouter.com/v6/components/route\n */\nfunction Route(_props) {\n   true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, \"A <Route> is only ever to be used as the child of <Routes> element, \" + \"never rendered directly. Please wrap your <Route> in a <Routes>.\") : 0 ;\n}\n/**\n * Provides location context for the rest of the app.\n *\n * Note: You usually won't render a `<Router>` directly. Instead, you'll render a\n * router that is more specific to your environment such as a `<BrowserRouter>`\n * in web browsers or a `<StaticRouter>` for server rendering.\n *\n * @see https://reactrouter.com/v6/router-components/router\n */\nfunction Router(_ref5) {\n  let {\n    basename: basenameProp = \"/\",\n    children = null,\n    location: locationProp,\n    navigationType = _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.Action.Pop,\n    navigator,\n    static: staticProp = false,\n    future\n  } = _ref5;\n  !!useInRouterContext() ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, \"You cannot render a <Router> inside another <Router>.\" + \" You should never have more than one in your app.\") : 0 : void 0;\n\n  // Preserve trailing slashes on basename, so we can let the user control\n  // the enforcement of trailing slashes throughout the app\n  let basename = basenameProp.replace(/^\\/*/, \"/\");\n  let navigationContext = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n    basename,\n    navigator,\n    static: staticProp,\n    future: _extends({\n      v7_relativeSplatPath: false\n    }, future)\n  }), [basename, future, navigator, staticProp]);\n  if (typeof locationProp === \"string\") {\n    locationProp = (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.parsePath)(locationProp);\n  }\n  let {\n    pathname = \"/\",\n    search = \"\",\n    hash = \"\",\n    state = null,\n    key = \"default\"\n  } = locationProp;\n  let locationContext = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    let trailingPathname = (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.stripBasename)(pathname, basename);\n    if (trailingPathname == null) {\n      return null;\n    }\n    return {\n      location: {\n        pathname: trailingPathname,\n        search,\n        hash,\n        state,\n        key\n      },\n      navigationType\n    };\n  }, [basename, pathname, search, hash, state, key, navigationType]);\n   true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(locationContext != null, \"<Router basename=\\\"\" + basename + \"\\\"> is not able to match the URL \" + (\"\\\"\" + pathname + search + hash + \"\\\" because it does not start with the \") + \"basename, so the <Router> won't render anything.\") : 0;\n  if (locationContext == null) {\n    return null;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(NavigationContext.Provider, {\n    value: navigationContext\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(LocationContext.Provider, {\n    children: children,\n    value: locationContext\n  }));\n}\n/**\n * A container for a nested tree of `<Route>` elements that renders the branch\n * that best matches the current location.\n *\n * @see https://reactrouter.com/v6/components/routes\n */\nfunction Routes(_ref6) {\n  let {\n    children,\n    location\n  } = _ref6;\n  return useRoutes(createRoutesFromChildren(children), location);\n}\n/**\n * Component to use for rendering lazily loaded data from returning defer()\n * in a loader function\n */\nfunction Await(_ref7) {\n  let {\n    children,\n    errorElement,\n    resolve\n  } = _ref7;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AwaitErrorBoundary, {\n    resolve: resolve,\n    errorElement: errorElement\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(ResolveAwait, null, children));\n}\nvar AwaitRenderStatus = /*#__PURE__*/function (AwaitRenderStatus) {\n  AwaitRenderStatus[AwaitRenderStatus[\"pending\"] = 0] = \"pending\";\n  AwaitRenderStatus[AwaitRenderStatus[\"success\"] = 1] = \"success\";\n  AwaitRenderStatus[AwaitRenderStatus[\"error\"] = 2] = \"error\";\n  return AwaitRenderStatus;\n}(AwaitRenderStatus || {});\nconst neverSettledPromise = new Promise(() => {});\nclass AwaitErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error(\"<Await> caught the following error during render\", error, errorInfo);\n  }\n  render() {\n    let {\n      children,\n      errorElement,\n      resolve\n    } = this.props;\n    let promise = null;\n    let status = AwaitRenderStatus.pending;\n    if (!(resolve instanceof Promise)) {\n      // Didn't get a promise - provide as a resolved promise\n      status = AwaitRenderStatus.success;\n      promise = Promise.resolve();\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_data\", {\n        get: () => resolve\n      });\n    } else if (this.state.error) {\n      // Caught a render error, provide it as a rejected promise\n      status = AwaitRenderStatus.error;\n      let renderError = this.state.error;\n      promise = Promise.reject().catch(() => {}); // Avoid unhandled rejection warnings\n      Object.defineProperty(promise, \"_tracked\", {\n        get: () => true\n      });\n      Object.defineProperty(promise, \"_error\", {\n        get: () => renderError\n      });\n    } else if (resolve._tracked) {\n      // Already tracked promise - check contents\n      promise = resolve;\n      status = \"_error\" in promise ? AwaitRenderStatus.error : \"_data\" in promise ? AwaitRenderStatus.success : AwaitRenderStatus.pending;\n    } else {\n      // Raw (untracked) promise - track it\n      status = AwaitRenderStatus.pending;\n      Object.defineProperty(resolve, \"_tracked\", {\n        get: () => true\n      });\n      promise = resolve.then(data => Object.defineProperty(resolve, \"_data\", {\n        get: () => data\n      }), error => Object.defineProperty(resolve, \"_error\", {\n        get: () => error\n      }));\n    }\n    if (status === AwaitRenderStatus.error && promise._error instanceof _remix_run_router__WEBPACK_IMPORTED_MODULE_1__.AbortedDeferredError) {\n      // Freeze the UI by throwing a never resolved promise\n      throw neverSettledPromise;\n    }\n    if (status === AwaitRenderStatus.error && !errorElement) {\n      // No errorElement, throw to the nearest route-level error boundary\n      throw promise._error;\n    }\n    if (status === AwaitRenderStatus.error) {\n      // Render via our errorElement\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: errorElement\n      });\n    }\n    if (status === AwaitRenderStatus.success) {\n      // Render children with resolved value\n      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(AwaitContext.Provider, {\n        value: promise,\n        children: children\n      });\n    }\n\n    // Throw to the suspense boundary\n    throw promise;\n  }\n}\n\n/**\n * @private\n * Indirection to leverage useAsyncValue for a render-prop API on `<Await>`\n */\nfunction ResolveAwait(_ref8) {\n  let {\n    children\n  } = _ref8;\n  let data = useAsyncValue();\n  let toRender = typeof children === \"function\" ? children(data) : children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, toRender);\n}\n\n///////////////////////////////////////////////////////////////////////////////\n// UTILS\n///////////////////////////////////////////////////////////////////////////////\n\n/**\n * Creates a route config from a React \"children\" object, which is usually\n * either a `<Route>` element or an array of them. Used internally by\n * `<Routes>` to create a route config from its children.\n *\n * @see https://reactrouter.com/v6/utils/create-routes-from-children\n */\nfunction createRoutesFromChildren(children, parentPath) {\n  if (parentPath === void 0) {\n    parentPath = [];\n  }\n  let routes = [];\n  react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (element, index) => {\n    if (! /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(element)) {\n      // Ignore non-elements. This allows people to more easily inline\n      // conditionals in their route config.\n      return;\n    }\n    let treePath = [...parentPath, index];\n    if (element.type === react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n      // Transparently support React.Fragment and its children.\n      routes.push.apply(routes, createRoutesFromChildren(element.props.children, treePath));\n      return;\n    }\n    !(element.type === Route) ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, \"[\" + (typeof element.type === \"string\" ? element.type : element.type.name) + \"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>\") : 0 : void 0;\n    !(!element.props.index || !element.props.children) ?  true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_invariant)(false, \"An index route cannot have child routes.\") : 0 : void 0;\n    let route = {\n      id: element.props.id || treePath.join(\"-\"),\n      caseSensitive: element.props.caseSensitive,\n      element: element.props.element,\n      Component: element.props.Component,\n      index: element.props.index,\n      path: element.props.path,\n      loader: element.props.loader,\n      action: element.props.action,\n      errorElement: element.props.errorElement,\n      ErrorBoundary: element.props.ErrorBoundary,\n      hasErrorBoundary: element.props.ErrorBoundary != null || element.props.errorElement != null,\n      shouldRevalidate: element.props.shouldRevalidate,\n      handle: element.props.handle,\n      lazy: element.props.lazy\n    };\n    if (element.props.children) {\n      route.children = createRoutesFromChildren(element.props.children, treePath);\n    }\n    routes.push(route);\n  });\n  return routes;\n}\n\n/**\n * Renders the result of `matchRoutes()` into a React element.\n */\nfunction renderMatches(matches) {\n  return _renderMatches(matches);\n}\n\nfunction mapRouteProperties(route) {\n  let updates = {\n    // Note: this check also occurs in createRoutesFromChildren so update\n    // there if you change this -- please and thank you!\n    hasErrorBoundary: route.ErrorBoundary != null || route.errorElement != null\n  };\n  if (route.Component) {\n    if (true) {\n      if (route.element) {\n         true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(false, \"You should not include both `Component` and `element` on your route - \" + \"`Component` will be used.\") : 0;\n      }\n    }\n    Object.assign(updates, {\n      element: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(route.Component),\n      Component: undefined\n    });\n  }\n  if (route.HydrateFallback) {\n    if (true) {\n      if (route.hydrateFallbackElement) {\n         true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(false, \"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - \" + \"`HydrateFallback` will be used.\") : 0;\n      }\n    }\n    Object.assign(updates, {\n      hydrateFallbackElement: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(route.HydrateFallback),\n      HydrateFallback: undefined\n    });\n  }\n  if (route.ErrorBoundary) {\n    if (true) {\n      if (route.errorElement) {\n         true ? (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.UNSAFE_warning)(false, \"You should not include both `ErrorBoundary` and `errorElement` on your route - \" + \"`ErrorBoundary` will be used.\") : 0;\n      }\n    }\n    Object.assign(updates, {\n      errorElement: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(route.ErrorBoundary),\n      ErrorBoundary: undefined\n    });\n  }\n  return updates;\n}\nfunction createMemoryRouter(routes, opts) {\n  return (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.createRouter)({\n    basename: opts == null ? void 0 : opts.basename,\n    future: _extends({}, opts == null ? void 0 : opts.future, {\n      v7_prependBasename: true\n    }),\n    history: (0,_remix_run_router__WEBPACK_IMPORTED_MODULE_1__.createMemoryHistory)({\n      initialEntries: opts == null ? void 0 : opts.initialEntries,\n      initialIndex: opts == null ? void 0 : opts.initialIndex\n    }),\n    hydrationData: opts == null ? void 0 : opts.hydrationData,\n    routes,\n    mapRouteProperties,\n    dataStrategy: opts == null ? void 0 : opts.dataStrategy,\n    patchRoutesOnNavigation: opts == null ? void 0 : opts.patchRoutesOnNavigation\n  }).initialize();\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/react-router/dist/index.js\n");

/***/ })

};
;