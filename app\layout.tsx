import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import '../src/index.css';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import AppSidebar from '@/components/AppSidebar';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Sentinel Dashboard',
  description: 'QR Code Authentication Dashboard',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="flex min-h-screen bg-gray-100 dark:bg-gray-900">
          <SidebarProvider>
            <AppSidebar />
            <SidebarInset className="bg-gray-100">
              <header className="flex h-16 shrink-0 items-center gap-2 border-b bg-background px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator orientation="vertical" className="mx-2 h-4" />
                <div className="text-xl font-semibold" id="page-title">
                  Dashboard
                </div>
              </header>
              <div className="flex-1 p-4 md:p-6 overflow-auto">
                {children}
              </div>
            </SidebarInset>
          </SidebarProvider>
        </div>
      </body>
    </html>
  );
}
