'use client';

import { useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AlertTriangle, CheckCircle, Clock, XCircle } from 'lucide-react';

// Mock alerts data
const alerts = [
  {
    id: 1,
    type: 'security',
    title: 'Tampered QR Code Detected',
    description: 'QR code tampering detected in Berlin, Germany',
    severity: 'high',
    status: 'active',
    timestamp: '2025-05-01 14:30:00',
    location: 'Berlin, Germany',
  },
  {
    id: 2,
    type: 'system',
    title: 'Printer Offline',
    description: 'Printer2 (Konica Minolta XYZ) has gone offline',
    severity: 'medium',
    status: 'acknowledged',
    timestamp: '2025-05-01 12:15:00',
    location: 'Manufacturing Floor B',
  },
  {
    id: 3,
    type: 'security',
    title: 'Multiple Failed Scans',
    description: 'Unusual pattern of failed QR scans detected',
    severity: 'medium',
    status: 'resolved',
    timestamp: '2025-05-01 09:45:00',
    location: 'Hong Kong',
  },
  {
    id: 4,
    type: 'system',
    title: 'High Usage Alert',
    description: 'QR code generation approaching monthly limit',
    severity: 'low',
    status: 'active',
    timestamp: '2025-05-01 08:00:00',
    location: 'System',
  },
];

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'acknowledged':
      return <Clock className="h-4 w-4 text-yellow-500" />;
    case 'resolved':
      return <CheckCircle className="h-4 w-4 text-green-500" />;
    default:
      return <XCircle className="h-4 w-4 text-gray-500" />;
  }
};

const getSeverityBadge = (severity: string) => {
  switch (severity) {
    case 'high':
      return <Badge variant="destructive">High</Badge>;
    case 'medium':
      return <Badge variant="secondary">Medium</Badge>;
    case 'low':
      return <Badge variant="outline">Low</Badge>;
    default:
      return <Badge variant="outline">Unknown</Badge>;
  }
};

export default function Alerts() {
  // Update page title
  useEffect(() => {
    const titleElement = document.getElementById('page-title');
    if (titleElement) {
      titleElement.textContent = 'Alerts';
    }
  }, []);

  const activeAlerts = alerts.filter(alert => alert.status === 'active').length;
  const acknowledgedAlerts = alerts.filter(alert => alert.status === 'acknowledged').length;
  const resolvedAlerts = alerts.filter(alert => alert.status === 'resolved').length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Alerts</h2>
        <Button size="sm">Configure Alerts</Button>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{activeAlerts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Acknowledged
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{acknowledgedAlerts}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Resolved Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{resolvedAlerts}</div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Alerts</CardTitle>
          <CardDescription>
            Monitor and manage security and system alerts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">Status</TableHead>
                <TableHead>Alert</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Time</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {alerts.map((alert) => (
                <TableRow key={alert.id}>
                  <TableCell>{getStatusIcon(alert.status)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{alert.title}</div>
                      <div className="text-sm text-muted-foreground">
                        {alert.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{getSeverityBadge(alert.severity)}</TableCell>
                  <TableCell>{alert.location}</TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {alert.timestamp}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      {alert.status === 'active' && (
                        <Button variant="outline" size="sm">
                          Acknowledge
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        Details
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
